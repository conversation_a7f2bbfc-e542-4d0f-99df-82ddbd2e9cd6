<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

#[MongoDB\EmbeddedDocument]
class UserPsaId
{
    #[MongoDB\Field(type: 'string')]
    private ?string $psaId = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $brand = null;

    public function getPsaId(): ?string
    {
        return $this->psaId;
    }

    public function setPsaId(?string $psaId): self
    {
        $this->psaId = $psaId;
        return $this;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(?string $brand): self
    {
        $this->brand = $brand;
        return $this;
    }

    /**
     * Convert the document to array format
     */
    public function toArray(): array
    {
        return [
            'psaId' => $this->psaId,
            'brand' => $this->brand,
        ];
    }
}
