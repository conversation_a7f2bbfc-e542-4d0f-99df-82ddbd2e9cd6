<?php

namespace Space\MongoDocuments\Document;

use Doctrine\ODM\MongoDB\Mapping\Annotations as MongoDB;

#[MongoDB\EmbeddedDocument]
class Push
{
    #[MongoDB\Field(type: 'string')]
    private ?string $deviceID = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $brand = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $country = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $pushToken = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $appId = null;

    #[MongoDB\Field(type: 'string')]
    private ?string $sessionId = null;

    #[MongoDB\Field(type: 'date')]
    private ?\DateTime $createdAt = null;

    #[MongoDB\Field(type: 'date')]
    private ?\DateTime $updatedAt = null;

    public function getDeviceID(): ?string
    {
        return $this->deviceID;
    }

    public function setDeviceID(?string $deviceID): self
    {
        $this->deviceID = $deviceID;
        return $this;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(?string $brand): self
    {
        $this->brand = $brand;
        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;
        return $this;
    }

    public function getPushToken(): ?string
    {
        return $this->pushToken;
    }

    public function setPushToken(?string $pushToken): self
    {
        $this->pushToken = $pushToken;
        return $this;
    }

    public function getAppId(): ?string
    {
        return $this->appId;
    }

    public function setAppId(?string $appId): self
    {
        $this->appId = $appId;
        return $this;
    }

    public function getSessionId(): ?string
    {
        return $this->sessionId;
    }

    public function setSessionId(?string $sessionId): self
    {
        $this->sessionId = $sessionId;
        return $this;
    }

    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(?\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     * Convert the document to array format
     */
    public function toArray(): array
    {
        return [
            'deviceID' => $this->deviceID,
            'brand' => $this->brand,
            'country' => $this->country,
            'pushToken' => $this->pushToken,
            'appId' => $this->appId,
            'sessionId' => $this->sessionId,
            'createdAt' => $this->createdAt?->format('Y-m-d H:i:s'),
            'updatedAt' => $this->updatedAt?->format('Y-m-d H:i:s'),
        ];
    }
}
