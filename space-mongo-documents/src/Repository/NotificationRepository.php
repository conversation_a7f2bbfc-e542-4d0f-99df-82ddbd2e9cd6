<?php

namespace Space\MongoDocuments\Repository;

use Doctrine\ODM\MongoDB\Repository\DocumentRepository;
use Space\MongoDocuments\Document\Notification;

/**
 * NotificationRepository for custom notification queries
 * 
 * This repository provides custom query methods for notification operations
 * including filtering by user, VIN, timestamp ranges, and aggregation operations.
 */
class NotificationRepository extends DocumentRepository
{
    /**
     * Find notification by user ID
     */
    public function findByUserId(string $userId): ?Notification
    {
        return $this->findOneBy(['userId' => $userId]);
    }

    /**
     * Find notifications by user ID and VIN
     */
    public function findByUserIdAndVin(string $userId, string $vin): ?Notification
    {
        return $this->createQueryBuilder()
            ->field('userId')->equals($userId)
            ->field('messages.vin')->equals($vin)
            ->getQuery()
            ->getSingleResult();
    }

    /**
     * Get vehicle notifications with aggregation pipeline
     * This method replicates the MongoDB Atlas aggregation pipeline functionality
     */
    public function getVehicleNotifications(string $userId, string $vin, array $filters = []): array
    {
        $qb = $this->createQueryBuilder()
            ->field('userId')->equals($userId);

        // Apply filters if provided
        if (isset($filters['since']) && $filters['since'] !== null) {
            $qb->field('messages.notificationData.timestamp')->gte((int)$filters['since']);
        }

        if (isset($filters['till']) && $filters['till'] !== null) {
            $qb->field('messages.notificationData.timestamp')->lte((int)$filters['till']);
        }

        if (isset($filters['limit']) && $filters['limit'] !== null) {
            $qb->limit((int)$filters['limit']);
        }

        if (isset($filters['offset']) && $filters['offset'] !== null) {
            $qb->skip((int)$filters['offset']);
        }

        $notification = $qb->getQuery()->getSingleResult();

        if (!$notification) {
            return [
                'vin' => $vin,
                'offset' => (int)($filters['offset'] ?? 0),
                'count' => 0,
                'items' => []
            ];
        }

        // Filter messages by VIN and process them
        $filteredMessages = [];
        foreach ($notification->getMessages() as $message) {
            if ($message->getVin() === $vin && $message->getNotificationData()) {
                $filteredMessages[] = $message->getNotificationData()->toArray();
            }
        }

        // Sort by timestamp descending
        usort($filteredMessages, function ($a, $b) {
            return ($b['timestamp'] ?? 0) <=> ($a['timestamp'] ?? 0);
        });

        return [
            'vin' => $vin,
            'offset' => (int)($filters['offset'] ?? 0),
            'count' => count($filteredMessages),
            'items' => $filteredMessages
        ];
    }

    /**
     * Get user vehicle notifications with aggregation
     * This method replicates the user notifications aggregation pipeline
     */
    public function getUserVehicleNotifications(string $userId, array $filters = []): array
    {
        $qb = $this->createQueryBuilder()
            ->field('userId')->equals($userId);

        // Apply filters if provided
        if (isset($filters['since']) && $filters['since'] !== null) {
            $qb->field('messages.notificationData.timestamp')->gte((int)$filters['since']);
        }

        if (isset($filters['till']) && $filters['till'] !== null) {
            $qb->field('messages.notificationData.timestamp')->lte((int)$filters['till']);
        }

        if (isset($filters['limit']) && $filters['limit'] !== null) {
            $qb->limit((int)$filters['limit']);
        }

        if (isset($filters['offset']) && $filters['offset'] !== null) {
            $qb->skip((int)$filters['offset']);
        }

        $notification = $qb->getQuery()->getSingleResult();

        if (!$notification) {
            return [];
        }

        // Group messages by VIN
        $groupedByVin = [];
        foreach ($notification->getMessages() as $message) {
            if ($message->getNotificationData()) {
                $vin = $message->getVin();
                if (!isset($groupedByVin[$vin])) {
                    $groupedByVin[$vin] = [];
                }
                $groupedByVin[$vin][] = $message->getNotificationData()->toArray();
            }
        }

        // Format the result
        $result = [];
        foreach ($groupedByVin as $vin => $messages) {
            // Sort messages by timestamp descending
            usort($messages, function ($a, $b) {
                return ($b['timestamp'] ?? 0) <=> ($a['timestamp'] ?? 0);
            });

            $result[] = [
                'vin' => $vin,
                'count' => count($messages),
                'items' => $messages
            ];
        }

        return $result;
    }

    /**
     * Remove expired notifications
     */
    public function removeExpiredNotifications(): int
    {
        $currentTime = time();
        
        return $this->createQueryBuilder()
            ->updateMany()
            ->field('messages.expiresAt')->lt($currentTime)
            ->field('messages')->pull(['expiresAt' => ['$lt' => $currentTime]])
            ->getQuery()
            ->execute();
    }
}
